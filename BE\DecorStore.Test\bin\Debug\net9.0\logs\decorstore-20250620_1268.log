[2025-06-20 11:08:32.117 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:32.120 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:32.129 +07:00 INF] Cache warmup completed in 8.3391ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:32.129 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:32.129 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:32.130 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:32.131 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:32.151 +07:00 WRN] Request failed with error: User with this email already exists, ErrorCode: USER_ALREADY_EXISTS, CorrelationId: 00-6841a51579944e3333337a59a709f1b5-491957b62b08c0b6-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"a4e1306b-f5b4-4ff5-85cd-0d3a36a5db7d","ActionName":"DecorStore.API.Controllers.AuthController.Register (DecorStore.API)","RequestId":"0HNDFM08S4NQU","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:32.290 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:32.290 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
