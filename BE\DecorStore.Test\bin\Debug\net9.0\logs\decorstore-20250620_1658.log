[2025-06-20 11:37:16.054 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:37:16.064 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:37:16.079 +07:00 INF] Cache warmup completed in 14.0564ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:37:16.079 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:37:16.079 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:37:16.081 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:37:16.082 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:37:16.215 +07:00 WRN] Request failed with error: Banner not found, ErrorCode: NOT_FOUND, CorrelationId: 00-b0921f0cd568f824b41b356551208fb0-4ce64fc8f2eedac1-00 {"SourceContext":"DecorStore.API.Controllers.BannerController","ActionId":"da56f9fa-da4a-468b-b722-489fe4793e7c","ActionName":"DecorStore.API.Controllers.BannerController.DeleteBanner (DecorStore.API)","RequestId":"0HNDFMGGGPCGK","RequestPath":"/api/Banner/99999","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:37:16.216 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:37:16.216 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
