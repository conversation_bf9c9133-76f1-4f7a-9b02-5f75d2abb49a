[2025-06-20 11:08:30.905 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:30.933 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:30.943 +07:00 INF] Cache warmup completed in 10.0962ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:30.943 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:30.943 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:30.958 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:30.959 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:31.139 +07:00 INF] Generating security dashboard data from "2025-06-13T04:08:31.1396849Z" to "2025-06-20T04:08:31.1396853Z" {"SourceContext":"DecorStore.API.Services.SecurityDashboardService","ActionId":"b65f34fe-ba70-477c-af4d-e3855098aa2f","ActionName":"DecorStore.API.Controllers.SecurityDashboardController.GetDashboardData (DecorStore.API)","RequestId":"0HNDFM08S4NPV","RequestPath":"/api/SecurityDashboard/dashboard","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:31.142 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:31.142 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
