[2025-06-20 11:08:31.329 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:31.333 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:31.353 +07:00 INF] Cache warmup completed in 19.9115ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:31.353 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:31.353 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:31.354 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:31.354 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:31.565 +07:00 WRN] Request failed with error: INVALID_STATUS, ErrorCode: Order status cannot be null or empty, CorrelationId: 00-69b692967f139e1bdc34c9cdeb257a8a-b4b33db87d098b45-00 {"SourceContext":"DecorStore.API.Controllers.OrderController","ActionId":"0cef38b6-2c5e-4f4e-9dd4-bd4d0d4e2d6d","ActionName":"DecorStore.API.Controllers.OrderController.UpdateOrderStatus (DecorStore.API)","RequestId":"0HNDFM08S4NQH","RequestPath":"/api/Order/1/status","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:31.567 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:31.567 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
