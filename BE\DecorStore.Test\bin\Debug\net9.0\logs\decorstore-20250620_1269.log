[2025-06-20 11:08:32.176 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:32.179 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:32.207 +07:00 INF] Cache warmup completed in 28.2851ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:32.208 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:32.208 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:32.209 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:32.209 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:32.347 +07:00 WRN] Redis connection not available for pattern removal: products* {"SourceContext":"DecorStore.API.Extensions.DistributedCacheService","ActionId":"b288ed01-a339-44a6-bfb2-311bb4266501","ActionName":"DecorStore.API.Controllers.PerformanceController.ClearCacheByPrefix (DecorStore.API)","RequestId":"0HNDFM08S4NR5","RequestPath":"/api/Performance/cache/clear/products","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:32.347 +07:00 INF] Cache cleared for prefix products by admin user {"SourceContext":"DecorStore.API.Controllers.PerformanceController","ActionId":"b288ed01-a339-44a6-bfb2-311bb4266501","ActionName":"DecorStore.API.Controllers.PerformanceController.ClearCacheByPrefix (DecorStore.API)","RequestId":"0HNDFM08S4NR5","RequestPath":"/api/Performance/cache/clear/products","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:32.348 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:32.348 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
