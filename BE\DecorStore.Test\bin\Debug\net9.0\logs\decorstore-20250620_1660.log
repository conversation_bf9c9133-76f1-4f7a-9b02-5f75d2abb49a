[2025-06-20 11:37:16.576 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:37:16.579 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:37:16.594 +07:00 INF] Cache warmup completed in 15.1976ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:37:16.594 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:37:16.595 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:37:16.596 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:37:16.597 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:37:16.738 +07:00 WRN] Request failed with error: Start date cannot be after end date, ErrorCode: INVALID_DATE_RANGE, CorrelationId: 00-3911f23e20156a6f04c2483844aa59ed-fcaffbf95046c43f-00 {"SourceContext":"DecorStore.API.Controllers.BannerController","ActionId":"6e674a63-0d7b-41d2-83d2-eb105ba22d42","ActionName":"DecorStore.API.Controllers.BannerController.CreateBanner (DecorStore.API)","RequestId":"0HNDFMGGGPCGP","RequestPath":"/api/Banner","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:37:16.739 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:37:16.739 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
