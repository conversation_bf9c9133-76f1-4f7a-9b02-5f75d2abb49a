using FluentValidation;
using FluentValidation.AspNetCore;
using DecorStore.API.Middleware;

namespace DecorStore.API.Extensions
{
    /// <summary>
    /// Extension methods for configuring validation services
    /// </summary>
    public static class ValidationServiceExtensions
    {        /// <summary>
        /// Adds comprehensive validation services to the DI container
        /// </summary>
        public static IServiceCollection AddValidationServices(this IServiceCollection services, IConfiguration configuration)
        {
            // Add FluentValidation
            services.AddFluentValidationAutoValidation()
                    .AddFluentValidationClientsideAdapters()                    .AddValidatorsFromAssemblyContaining<Program>(ServiceLifetime.Scoped, filter: result =>
                    {
                        // Exclude validators with async rules that are incompatible with ASP.NET validation pipeline
                        return !result.ValidatorType.Name.Contains("ProductAvailabilityValidator") &&
                               !result.ValidatorType.Name.Contains("UniqueSkuValidator") &&
                               !result.ValidatorType.Name.Contains("CategoryExistsValidator") &&
                               !result.ValidatorType.Name.Contains("AddToCartValidator");
                    });

            // Register middleware services that implement IMiddleware
            services.AddScoped<GlobalExceptionHandlerMiddleware>();

            return services;
        }        /// <summary>
        /// Configures validation middleware in the request pipeline
        /// </summary>
        public static WebApplication UseValidationMiddleware(this WebApplication app)
        {
            // Add input sanitization (before authentication/authorization)
            app.UseMiddleware<InputSanitizationMiddleware>();

            return app;
        }
    }
}
