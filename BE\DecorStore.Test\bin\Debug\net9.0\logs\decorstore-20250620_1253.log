[2025-06-20 11:08:31.135 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:31.138 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:31.150 +07:00 INF] Cache warmup completed in 11.3119ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:31.150 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:31.150 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:31.162 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:31.163 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:31.363 +07:00 INF] Starting comprehensive vulnerability assessment {"SourceContext":"DecorStore.API.Services.SecurityTestingService","ActionId":"19cd1c6f-89e1-4957-9b2f-e65286790c46","ActionName":"DecorStore.API.Controllers.SecurityTestingController.PerformVulnerabilityAssessment (DecorStore.API)","RequestId":"0HNDFM08S4NQ9","RequestPath":"/api/SecurityTesting/vulnerability-assessment","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:31.363 +07:00 INF] Testing SQL injection vulnerabilities {"SourceContext":"DecorStore.API.Services.SecurityTestingService","ActionId":"19cd1c6f-89e1-4957-9b2f-e65286790c46","ActionName":"DecorStore.API.Controllers.SecurityTestingController.PerformVulnerabilityAssessment (DecorStore.API)","RequestId":"0HNDFM08S4NQ9","RequestPath":"/api/SecurityTesting/vulnerability-assessment","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:31.363 +07:00 INF] Testing XSS vulnerabilities {"SourceContext":"DecorStore.API.Services.SecurityTestingService","ActionId":"19cd1c6f-89e1-4957-9b2f-e65286790c46","ActionName":"DecorStore.API.Controllers.SecurityTestingController.PerformVulnerabilityAssessment (DecorStore.API)","RequestId":"0HNDFM08S4NQ9","RequestPath":"/api/SecurityTesting/vulnerability-assessment","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:31.363 +07:00 INF] Testing authentication mechanisms {"SourceContext":"DecorStore.API.Services.SecurityTestingService","ActionId":"19cd1c6f-89e1-4957-9b2f-e65286790c46","ActionName":"DecorStore.API.Controllers.SecurityTestingController.PerformVulnerabilityAssessment (DecorStore.API)","RequestId":"0HNDFM08S4NQ9","RequestPath":"/api/SecurityTesting/vulnerability-assessment","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:31.363 +07:00 INF] Testing authorization controls {"SourceContext":"DecorStore.API.Services.SecurityTestingService","ActionId":"19cd1c6f-89e1-4957-9b2f-e65286790c46","ActionName":"DecorStore.API.Controllers.SecurityTestingController.PerformVulnerabilityAssessment (DecorStore.API)","RequestId":"0HNDFM08S4NQ9","RequestPath":"/api/SecurityTesting/vulnerability-assessment","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:31.364 +07:00 INF] Testing input validation {"SourceContext":"DecorStore.API.Services.SecurityTestingService","ActionId":"19cd1c6f-89e1-4957-9b2f-e65286790c46","ActionName":"DecorStore.API.Controllers.SecurityTestingController.PerformVulnerabilityAssessment (DecorStore.API)","RequestId":"0HNDFM08S4NQ9","RequestPath":"/api/SecurityTesting/vulnerability-assessment","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:31.364 +07:00 INF] Testing rate limiting effectiveness {"SourceContext":"DecorStore.API.Services.SecurityTestingService","ActionId":"19cd1c6f-89e1-4957-9b2f-e65286790c46","ActionName":"DecorStore.API.Controllers.SecurityTestingController.PerformVulnerabilityAssessment (DecorStore.API)","RequestId":"0HNDFM08S4NQ9","RequestPath":"/api/SecurityTesting/vulnerability-assessment","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:31.364 +07:00 INF] Testing security configuration {"SourceContext":"DecorStore.API.Services.SecurityTestingService","ActionId":"19cd1c6f-89e1-4957-9b2f-e65286790c46","ActionName":"DecorStore.API.Controllers.SecurityTestingController.PerformVulnerabilityAssessment (DecorStore.API)","RequestId":"0HNDFM08S4NQ9","RequestPath":"/api/SecurityTesting/vulnerability-assessment","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:31.364 +07:00 INF] Checking dependency vulnerabilities {"SourceContext":"DecorStore.API.Services.SecurityTestingService","ActionId":"19cd1c6f-89e1-4957-9b2f-e65286790c46","ActionName":"DecorStore.API.Controllers.SecurityTestingController.PerformVulnerabilityAssessment (DecorStore.API)","RequestId":"0HNDFM08S4NQ9","RequestPath":"/api/SecurityTesting/vulnerability-assessment","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:31.385 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 11:08:31.385 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
